name: Plugin Release
on:
  push:
    tags:
      - "v*"

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.0"

      - name: Install dependencies
        run: composer install --no-dev --optimize-autoloader

      - name: Create plugin zip
        run: |
          zip -r schema-graph-builder.zip . \
            -x "*.git*" "node_modules/*" "*.github*" "composer.json" "composer.lock"

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          files: schema-graph-builder.zip
