name: Code Quality
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.0"

      - name: PHP Lint
        run: find . -name "*.php" -exec php -l {} \;

      - name: WordPress Coding Standards
        run: |
          composer require --dev wp-coding-standards/wpcs
          ./vendor/bin/phpcs --standard=WordPress .
