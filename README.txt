=== Schema Graph Builder ===
Contributors: mmskeleshteri
Donate link: https://github.com/keleshteri/wp-schema-graph-builder
Tags: schema, json-ld, seo, course, education, medical, graph, structured-data, rich-results
Requires at least: 5.0
Tested up to: 6.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Build interconnected schema graphs for WordPress. Generate multiple schema types in unified @graph structure with smart automation.

== Description ==

**Schema Graph Builder** is the first WordPress plugin designed specifically for creating **interconnected schema graphs** using JSON-LD @graph structure. Unlike traditional schema plugins that generate single schemas, Schema Graph Builder creates multiple related schema types that work together to provide comprehensive structured data.

= 🎯 Perfect For =

* **Educational Institutions** - Courses, programs, certifications
* **Medical Websites** - Healthcare courses, medical specialties, conditions
* **Training Companies** - Professional development, skill-based courses
* **E-learning Platforms** - Online courses, educational content

= 🚀 Core Features =

* **Multi-Schema Generation** - Create 5+ interconnected schema types per page
* **@Graph Structure** - Proper JSON-LD graph implementation for maximum SEO impact
* **Smart Auto-Mapping** - Automatically maps WordPress fields to schema properties
* **Educational Focus** - Specialized for Course, EducationalOccupationalProgram schemas
* **Medical Specialization** - Built-in support for medical/healthcare schemas
* **Template System** - Quick-setup templates for different course types
* **Yoast SEO Compatible** - Smart detection prevents schema conflicts
* **Visual Field Mapping** - Intuitive interface for custom field assignments
* **Real-time Preview** - See generated JSON-LD before publishing
* **Google Validation** - Built-in Rich Results testing integration

= 📋 Generated Schema Types =

**Educational Course Package:**
* Course - Educational offering details
* EducationalOccupationalProgram - Broader academic program context
* MedicalSpecialty - Medical domain expertise (when applicable)
* MedicalCondition - Health conditions covered (when applicable)  
* WebPage - Page information anchor (smart Yoast compatibility)

**Additional Schema Support:**
* Organization - Institution/provider details
* Person - Instructor information
* Review - Course reviews and ratings
* LocalBusiness - For medical practices and institutions

= 🤖 AI-Powered Features (Coming Soon) =

**🔮 Intelligent Content Analysis**
* Auto-detect instructors, duration, and specialties from course content
* Smart schema type recommendations based on content analysis
* Automatic field population from unstructured text

**⚡ Schema Optimization Engine**
* AI-powered schema quality scoring (1-100)
* Google compliance recommendations
* Competitor schema analysis and suggestions

**🎯 Performance Predictions**
* Rich results probability scoring
* Expected CTR improvement estimates
* Schema completeness ranking in your niche

**🌐 Multi-Language Support**
* Auto-translate schema content for international SEO
* Localized educational credential mapping
* Cultural adaptation for medical terminology

*Note: AI features are in active development and will be available in upcoming releases.*

= 💪 Why Choose Schema Graph Builder? =

**vs Schema Pro:**
* ✅ Multiple schemas per page (vs single schema)
* ✅ @graph structure support (vs standalone schemas)
* ✅ Educational/medical specialization (vs generic approach)

**vs Yoast SEO:**
* ✅ Advanced multi-schema capability (vs basic single schemas)
* ✅ Educational focus with specialized fields
* ✅ Smart auto-mapping reduces manual work

**vs Custom Development:**
* ✅ No coding required - visual interface
* ✅ Automatic Google compliance
* ✅ Regular updates and support