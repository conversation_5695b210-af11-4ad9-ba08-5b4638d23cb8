<?php

/**
 * The plugin bootstrap file
 *
 * This file is read by WordPress to generate the plugin information in the plugin
 * admin area. This file also includes all of the dependencies used by the plugin,
 * registers the activation and deactivation functions, and defines a function
 * that starts the plugin.
 *
 * @link              https://github.com/keleshteri
 * @since             1.0.0
 * @package           Schema_Graph_Builder
 *
 * @wordpress-plugin
 * Plugin Name:       Schema Graph Builder
 * Plugin URI:        https://github.com/keleshteri/wp-schema-graph-builder
 * Description:       Build interconnected schema graphs for WordPress. Generate multiple schema types (Course, Educational Program, Medical, WebPage) in unified @graph structure with smart field mapping and professional templates.
 * Version:           1.0.0
 * Author:            MMS Keleshteri
 * Author URI:        https://github.com/keleshteri/
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       schema-graph-builder
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Currently plugin version.
 * Start at version 1.0.0 and use SemVer - https://semver.org
 * Rename this for your plugin and update it as you release new versions.
 */
define( 'SCHEMA_GRAPH_BUILDER_VERSION', '1.0.0' );

/**
 * The code that runs during plugin activation.
 * This action is documented in includes/class-schema-graph-builder-activator.php
 */
function activate_schema_graph_builder() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-schema-graph-builder-activator.php';
	Schema_Graph_Builder_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 * This action is documented in includes/class-schema-graph-builder-deactivator.php
 */
function deactivate_schema_graph_builder() {
	require_once plugin_dir_path( __FILE__ ) . 'includes/class-schema-graph-builder-deactivator.php';
	Schema_Graph_Builder_Deactivator::deactivate();
}

register_activation_hook( __FILE__, 'activate_schema_graph_builder' );
register_deactivation_hook( __FILE__, 'deactivate_schema_graph_builder' );

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require plugin_dir_path( __FILE__ ) . 'includes/class-schema-graph-builder.php';

/**
 * Begins execution of the plugin.
 *
 * Since everything within the plugin is registered via hooks,
 * then kicking off the plugin from this point in the file does
 * not affect the page life cycle.
 *
 * @since    1.0.0
 */
function run_schema_graph_builder() {

	$plugin = new Schema_Graph_Builder();
	$plugin->run();

}
run_schema_graph_builder();
